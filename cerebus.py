#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import argparse
import sys
from datetime import datetime
from threading import Event

from modules.attack_core import AttackCore
from modules.logger import Logger
from modules.password_generator import PasswordGenerator
# --- NEW: Import the new PasswordScorer ---
from modules.password_scorer import PasswordScorer
from services import ssh, ftp, telnet, web_form, rdp

# --- NEW BANNER for CEREBUS ---
BANNER = r"""
  /$$$$$$$$ /$$$$$$$$ /$$$$$$$$ /$$$$$$$  /$$$$$$$  /$$   /$$ /$$$$$$$$
 | $$_____/| $$_____/| $$_____/| $$__  $$| $$__  $$| $$  /$$/| $$_____/
 | $$      | $$      | $$      | $$  \ $$| $$  \ $$| $$ /$$/ | $$
 | $$$$$   | $$$$$   | $$$$$   | $$$$$$$/| $$$$$$$/| $$$$$/  | $$$$$
 | $$__/   | $$__/   | $$__/   | $$__  $$| $$__  $$| $$  $$  | $$__/
 | $$      | $$      | $$      | $$  \ $$| $$  \ $$| $$|  $$ | $$
 | $$      | $$$$$$$$| $$$$$$$$| $$$$$$$/| $$$$$$$/| $$ \  $$| $$$$$$$$
 |__/      |________/|________/|_______/ |_______/ |__/  \__/|________/
            [ Generative Password Auditing Tool ]
"""

def main():
    print(BANNER)
    parser = argparse.ArgumentParser(description="Cerebus - A Generative Password Auditing Tool.")

    # --- NEW: Training arguments for both models ---
    train_group = parser.add_argument_group('Training Options')
    train_group.add_argument("--train-generator", metavar="PATH", help="Train the Markov password GENERATOR model on a wordlist and exit.")
    train_group.add_argument("--train-scorer", metavar="PATH", help="Train the probabilistic password SCORER model on a wordlist and exit.")

    # Attack Arguments
    attack_group = parser.add_argument_group('Attack Options')
    attack_group.add_argument("target", nargs='?', default=None, help="The target IP, hostname, or URL.")
    attack_group.add_argument("-l", "--user", help="The username to target.")
    attack_group.add_argument("-P", "--password-list", help="Path to a base password list.")
    attack_group.add_argument("-s", "--port", type=int, help="The target port.")
    attack_group.add_argument("-t", "--threads", type=int, default=10, help="Number of concurrent threads.")
    attack_group.add_argument("--service", choices=['ssh', 'ftp', 'telnet', 'web_form', 'rdp'], help="The service to attack.")
    attack_group.add_argument("--generative", action="store_true", help="Enable the password generator.")
    attack_group.add_argument("--gen-count", type=int, default=1000, help="Number of passwords to generate.")
    # --- NEW: Flag to enable the scorer ---
    attack_group.add_argument("--scorer", action="store_true", help="Enable probabilistic scoring to sort passwords (recommended).")

    # Web-specific arguments
    web_group = parser.add_argument_group('Web Attack Options')
    web_group.add_argument("--web-username-field", default="username", help="Username field name.")
    web_group.add_argument("--web-password-field", default="password", help="Password field name.")
    web_group.add_argument("--web-fail-string", help="String indicating a FAILED web login.")

    args = parser.parse_args()

    # --- Handle Training Modes ---
    if args.train_generator:
        generator = PasswordGenerator()
        generator.train(args.train_generator)
        sys.exit(0)

    if args.train_scorer:
        scorer = PasswordScorer()
        scorer.train(args.train_scorer)
        sys.exit(0)

    # --- Validate Attack Arguments ---
    if not all([args.target, args.user, args.service]):
        parser.error("For an attack, 'target', --user, and --service are required.")
    if not args.password_list and not args.generative:
        parser.error("You must provide a password list (-P) or enable generative mode (--generative).")

    # --- Service and Port Configuration ---
    service_map = {
        'ssh': {'module': ssh, 'port': 22}, 'ftp': {'module': ftp, 'port': 21},
        'telnet': {'module': telnet, 'port': 23}, 'web_form': {'module': web_form, 'port': 80},
        'rdp': {'module': rdp, 'port': 3389}
    }
    service_config = service_map[args.service]
    module = service_config['module']
    port = args.port or service_config['port']

    # --- Load Passwords ---
    passwords = []
    if args.password_list:
        try:
            with open(args.password_list, 'r', encoding='latin-1') as f:
                passwords = [line.strip() for line in f.readlines()]
            Logger.log_info(f"Loaded {len(passwords)} passwords from {args.password_list}")
        except FileNotFoundError:
            Logger.log_error(f"Password file not found: {args.password_list}")
            sys.exit(1)

    # --- Setup Attack ---
    stop_event = Event()
    start_time = datetime.now()
    attack_params = {}
    if args.service == 'web_form':
        attack_params = {'username_field': args.web_username_field, 'password_field': args.web_password_field, 'failure_string': args.web_fail_string}

    attack = AttackCore(
        target=args.target, port=port, username=args.user, passwords=passwords,
        threads=args.threads, service_module=module, stop_event=stop_event,
        service_name=args.service, attack_params=attack_params,
        use_generator=args.generative, gen_count=args.gen_count,
        use_scorer=args.scorer # Pass the new flag
    )

    # --- Run Attack ---
    try:
        attack.run()
    except KeyboardInterrupt:
        Logger.log_info("\n[!] Attack interrupted by user. Shutting down...")
        stop_event.set()
    finally:
        for thread in attack.thread_pool:
            if thread.is_alive():
                thread.join(timeout=1.0)
        end_time = datetime.now()
        Logger.log_info(f"Attack finished in {end_time - start_time}.")
        if not attack.password_found:
             Logger.log_error("Password not found.")

if __name__ == '__main__':
    main()