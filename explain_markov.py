#!/usr/bin/env python3

import json
from collections import defaultdict

def explain_markov_training():
    print("=== How Cerebus Password Generation Actually Works ===\n")
    
    # Simple example with a few passwords
    passwords = ["password", "pass123", "admin", "admin123"]
    state_size = 2
    
    print("1. TRAINING PHASE:")
    print(f"   Training passwords: {passwords}")
    print(f"   State size: {state_size} (looks at {state_size} characters to predict the next)")
    print()
    
    # Build the model like Cerebus does
    transitions = defaultdict(lambda: defaultdict(int))
    
    for password in passwords:
        print(f"   Processing '{password}':")
        for i in range(len(password) - state_size):
            state = password[i:i+state_size]
            next_char = password[i+state_size]
            transitions[state][next_char] += 1
            print(f"     '{state}' -> '{next_char}'")
        print()
    
    print("2. LEARNED TRANSITIONS:")
    for state, next_chars in sorted(transitions.items()):
        print(f"   '{state}' can be followed by: {dict(next_chars)}")
    print()
    
    print("3. GENERATION PHASE:")
    print("   - Starts with a random 2-character state (e.g., 'pa')")
    print("   - Looks up what characters can follow 'pa'")
    print("   - Randomly picks one based on frequency")
    print("   - Updates state to last 2 characters and repeats")
    print()
    
    print("4. EXAMPLE GENERATION:")
    print("   Start: 'pa'")
    print("   'pa' -> 's' (from 'password', 'pass123')")
    print("   State becomes 'as', password so far: 'pas'")
    print("   'as' -> 's' (from 'password', 'pass123')")  
    print("   State becomes 'ss', password so far: 'pass'")
    print("   'ss' -> 'w' or '1' (from training data)")
    print("   And so on...")
    print()
    
    print("5. WHY IT'S SMART:")
    print("   ✓ Learns character patterns and sequences")
    print("   ✓ Preserves realistic character transitions")
    print("   ✓ Can generate passwords NOT in training set")
    print("   ✓ Maintains statistical properties of real passwords")
    print("   ✗ But yes, it's recombining learned patterns")

if __name__ == "__main__":
    explain_markov_training()
