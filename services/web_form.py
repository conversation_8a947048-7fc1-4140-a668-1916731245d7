# services/web_form.py

import requests

def attempt_login(url, port, user, password, **kwargs):
    # Extract web-specific parameters
    username_field = kwargs.get('username_field')
    password_field = kwargs.get('password_field')
    failure_string = kwargs.get('failure_string')

    if not all([username_field, password_field, failure_string]):
        raise ValueError("Missing web form parameters: username_field, password_field, or failure_string")

    payload = {
        username_field: user,
        password_field: password,
        'Login': 'Login' # Common submit button value
    }
    
    try:
        # Using a session object can handle cookies automatically
        with requests.Session() as s:
            res = s.post(url, data=payload, timeout=5)
            # If the failure string is NOT in the response, assume success
            return failure_string not in res.text
    except requests.exceptions.RequestException:
        # Catches connection errors, timeouts, etc.
        raise
