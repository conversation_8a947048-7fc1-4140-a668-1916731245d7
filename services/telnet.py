# SmartBruteRL/services/telnet.py

import socket
import time

def attempt_login(host, port, user, password, **kwargs):
    """
    Attempts to authenticate against a Telnet service using the standard socket library.
    """
    try:
        # Create a socket and connect
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        s.settimeout(5) # Set a 5-second timeout for all operations
        s.connect((host, port))

        # Telnet negotiation can send control sequences, we'll just read past them
        time.sleep(0.5)
        s.recv(4096)

        # Look for login prompt
        s.sendall(user.encode('ascii') + b"\n")
        
        # Look for password prompt
        time.sleep(0.5)
        s.recv(4096)
        s.sendall(password.encode('ascii') + b"\n")

        # Wait for the server to respond
        time.sleep(0.5)
        response = s.recv(4096).decode('ascii', errors='ignore')
        s.close()

        # Check for common success or failure strings
        failure_strings = ["Login incorrect", "Login failed", "Wrong password"]
        success_prompts = ["$", "#", ">", "Last login:"]

        if any(fail_str in response for fail_str in failure_strings):
            return False
            
        if any(prompt in response for prompt in success_prompts):
            return True

        # If we can't determine, assume failure
        return False

    except (socket.timeout, ConnectionRefusedError, ConnectionResetError):
        # Any connection or timeout issue is treated as a failed attempt
        return False
    except Exception:
        # Re-raise any other unexpected errors
        raise