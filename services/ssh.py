# services/ssh.py

import paramiko

def attempt_login(host, port, user, password, **kwargs):
    client = paramiko.SSHClient()
    client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    try:
        client.connect(
            hostname=host,
            port=port,
            username=user,
            password=password,
            timeout=5,
            banner_timeout=200 # Handles slow banners
        )
        return True
    except paramiko.AuthenticationException:
        return False
    except Exception:
        # Catches connection errors, timeouts, etc.
        raise
    finally:
        client.close()
