# services/ftp.py

import ftplib

def attempt_login(host, port, user, password, **kwargs):
    try:
        with ftplib.FTP() as ftp:
            ftp.connect(host, port, timeout=5)
            ftp.login(user, password)
            ftp.quit()
        return True
    except ftplib.error_perm:
        # 530 Login incorrect
        return False
    except Exception:
        # Catches timeouts and connection errors
        raise
