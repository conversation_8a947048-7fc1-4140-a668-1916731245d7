#!/usr/bin/env python3

import json

def analyze_trained_model():
    print("=== Analysis of Cerebus's Actual Trained Model ===\n")
    
    with open('learned_models/markov_model.json', 'r') as f:
        model = json.load(f)
    
    transitions = model['transitions']
    
    print(f"State size: {model['state_size']}")
    print(f"Total learned transitions: {len(transitions)}")
    print()
    
    print("Examples of what the model learned:")
    print("(showing some interesting patterns)")
    print()
    
    # Show some interesting patterns
    interesting_states = ['pa', 'ad', '12', 'ss', 'er', 'in', 'or']
    
    for state in interesting_states:
        if state in transitions:
            next_chars = transitions[state]
            print(f"'{state}' -> {next_chars}")
            print(f"  (if password contains '{state}', next char could be: {list(next_chars.keys())})")
    
    print()
    print("What this means:")
    print("- The model learned that 'pa' is usually followed by 's' (from 'password')")
    print("- '12' is usually followed by '3' (from '123' patterns)")
    print("- 'ad' is followed by 'm' (from 'admin')")
    print("- And so on...")
    print()
    
    print("So when generating:")
    print("1. It picks a starting 2-char sequence")
    print("2. Uses probabilities to pick the next character")
    print("3. Slides the window and repeats")
    print("4. Creates new combinations of learned patterns")
    print()
    
    print("Example generated passwords we saw:")
    examples = ["abc123456", "microsoft", "word123456", "accessword12"]
    print("- " + "\n- ".join(examples))
    print()
    print("These combine patterns like:")
    print("- 'abc' + '123456' (number sequences)")
    print("- 'micro' + 'soft' (tech terms)")
    print("- 'word' + '123456' (word + numbers)")
    print("- 'access' + 'word' + '12' (compound words + numbers)")

if __name__ == "__main__":
    analyze_trained_model()
