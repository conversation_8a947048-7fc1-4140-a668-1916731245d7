# Cerebus - Generative Password Auditing Tool

Cerebus is a command-line password auditing tool that leverages machine learning to discover weak credentials. It combines traditional brute-force techniques with a generative password model and a probabilistic scorer to intelligently prioritize guesses.

---
## 🧠 Key Features

- **Multi-Protocol:** Attacks services over SSH, FTP, Telnet, Web Forms, and RDP.
- **Password Generator:** A Markov chain model can be trained on real-world wordlists to generate new, realistic password candidates.
- **Probabilistic Scorer:** A statistical model scores and ranks passwords, ensuring the most likely candidates are tried first.
- **Hybrid Attack Modes:** Combine wordlists with the generator and scorer for a comprehensive and intelligent attack strategy.
- **Modular Design:** Easy-to-extend architecture for adding new protocols and features.

---
## 🛠️ Setup

1.  **Clone the repository:**
    ```bash
    git clone [https://github.com/YOUR_USERNAME/Cerebus.git](https://github.com/YOUR_USERNAME/Cerebus.git)
    cd Cerebus
    ```

2.  **Create a Python virtual environment:**
    ```bash
    python3 -m venv venv
    source venv/bin/activate
    ```

3.  **Install dependencies:**
    ```bash
    pip install -r requirements.txt
    ```

4.  **Train the ML Models (One-Time Step):**
    Before attacking, you must train the generator and scorer models on a large wordlist.
    ```bash
    # Train the password creator
    python3 cerebus.py --train-generator /usr/share/wordlists/rockyou.txt

    # Train the password ranker
    python3 cerebus.py --train-scorer /usr/share/wordlists/rockyou.txt
    ```

---
## 🚀 Usage & Attack Strategies

Cerebus supports multiple attack strategies, from simple to advanced.

### ### 1. Standard Attack (Wordlist Only)

This is a classic brute-force attack using only the provided password list in its original order. It's fast and effective for checking against known lists.

```bash
# Example using a top-100 password list
python3 cerebus.py <TARGET_IP> -l <USER> -P top-100-passwords.txt --service ssh -t 10
# This command uses a small wordlist, generates 50,000 new candidates,
# scores and sorts them, and uses a low thread count to avoid detection.
python3 cerebus.py <TARGET_IP> -l <USER> -P top-100-passwords.txt --service ssh --generative --gen-count 50000 --scorer -t 2
# This command generates 1,000,000 candidates and ranks them with the scorer.
python3 cerebus.py <TARGET_IP> -l <USER> --service ssh --generative --gen-count 1000000 --scorer -t 2