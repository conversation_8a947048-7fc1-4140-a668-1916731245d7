# modules/password_scorer.py

import json
import os
import math
import sys  # Import the sys module
from collections import defaultdict
from modules.logger import Logger

class PasswordScorer:
    def __init__(self, model_path="learned_models/scorer_model.json"):
        self.model_path = model_path
        self.model = None
        self.load_model()

    def train(self, wordlist_path, n=3):
        """Builds a statistical n-gram model from a wordlist."""
        Logger.log_info(f"Training probabilistic scorer from {wordlist_path}...")
        
        counts = defaultdict(lambda: defaultdict(int))
        total_counts = defaultdict(int)
        
        with open(wordlist_path, 'r', encoding='latin-1') as f:
            for line in f:
                password = f"^{line.strip()}$"
                if len(password) < n:
                    continue
                
                for i in range(len(password) - (n - 1)):
                    ngram = password[i : i + n]
                    prefix = ngram[:-1]
                    char = ngram[-1]
                    counts[prefix][char] += 1
                    total_counts[prefix] += 1

        log_probs = defaultdict(dict)
        for prefix, char_counts in counts.items():
            total = total_counts[prefix]
            for char, count in char_counts.items():
                log_probs[prefix][char] = math.log10(count / total)
        
        self.model = {"n": n, "log_probs": dict(log_probs)}
        self._save_model()
        Logger.log_success(f"Scorer training complete. Model saved to {self.model_path}")

    def _save_model(self):
        if not os.path.exists("learned_models"):
            os.makedirs("learned_models")
        with open(self.model_path, 'w') as f:
            json.dump(self.model, f)

    def load_model(self):
        if os.path.exists(self.model_path):
            try:
                with open(self.model_path, 'r') as f:
                    self.model = json.load(f)
                Logger.log_info("Probabilistic scorer model loaded.")
            except (FileNotFoundError, json.JSONDecodeError):
                self.model = None
        else:
            self.model = None

    def score(self, password):
        """Calculates the log probability score of a password."""
        if not self.model:
            return 0

        n = self.model['n']
        password = f"^{password}$"
        total_log_prob = 0
        
        for i in range(len(password) - (n - 1)):
            ngram = password[i : i + n]
            prefix = ngram[:-1]
            char = ngram[-1]
            
            if prefix in self.model['log_probs'] and char in self.model['log_probs'][prefix]:
                total_log_prob += self.model['log_probs'][prefix][char]
            else:
                total_log_prob -= 10
        
        return total_log_prob / len(password) if password else 0

    def sort_passwords(self, passwords):
        """Sorts a list of passwords from most to least likely."""
        Logger.log_info("Scoring password candidates...")
        
        # --- NEW: Loop with progress bar ---
        scored_passwords = []
        total = len(passwords)
        for i, p in enumerate(passwords):
            scored_passwords.append((self.score(p), p))
            
            # Update the progress bar periodically for efficiency
            if (i + 1) % 500 == 0 or (i + 1) == total:
                progress = (i + 1) / total
                bar_len = 25
                bar = '█' * int(progress * bar_len)
                percent = int(progress * 100)
                sys.stdout.write(f'\r[*] Scoring... [{bar:<{bar_len}}] {percent}%')
                sys.stdout.flush()
        
        print() # Print a newline to move past the progress bar
        
        Logger.log_info("Sorting scored passwords...")
        scored_passwords.sort(key=lambda x: x[0], reverse=True)
        Logger.log_info("Sorting complete.")
        
        return [p for score, p in scored_passwords]