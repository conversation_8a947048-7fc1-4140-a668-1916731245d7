# modules/attack_core.py

import queue
import threading
import time
from modules.logger import Logger
from modules.password_generator import PasswordGenerator
from modules.password_scorer import PasswordScorer

class AttackCore:
    def __init__(self, target, port, username, passwords, threads, service_module, stop_event, service_name, attack_params={}, use_generator=False, gen_count=1000, use_scorer=False):
        self.target = target
        self.port = port
        self.username = username
        self.passwords = passwords
        self.threads = threads
        self.service_module = service_module
        self.stop_event = stop_event
        self.attack_params = attack_params
        self.use_generator = use_generator
        self.gen_count = gen_count
        self.use_scorer = use_scorer
        
        self.password_queue = queue.Queue()
        self.password_found = None
        self.thread_pool = []
        
        if self.use_generator:
            self.generator = PasswordGenerator()
        if self.use_scorer:
            self.scorer = PasswordScorer()

    def _worker(self, thread_id):
        # --- NEW LOGGING ---
        Logger.log_info(f"Worker thread {thread_id} started.")
        while not self.stop_event.is_set():
            try:
                password = self.password_queue.get(timeout=0.5)
                # --- NEW LOGGING ---
                Logger.log_info(f"Worker thread {thread_id} got password: {password}")
            except queue.Empty:
                Logger.log_info(f"Worker thread {thread_id} found queue empty, exiting.")
                return

            Logger.log_attempt(self.target, self.username, password, thread_id)
            try:
                if self.service_module.attempt_login(self.target, self.port, self.username, password, **self.attack_params):
                    if not self.password_found:
                        self.password_found = password
                        Logger.log_success(f"SUCCESS! Credentials found: {self.username}:{password}")
                        self.stop_event.set()
            except Exception as e:
                Logger.log_error(f"Thread {thread_id} encountered an error: {e}")
            finally:
                self.password_queue.task_done()
    
    def run(self):
        """Starts the brute-force attack."""
        combined_passwords = list(self.passwords)

        if self.use_generator:
            if self.generator and self.generator.model:
                Logger.log_info(f"Generating {self.gen_count} new password candidates...")
                for _ in range(self.gen_count):
                    new_pass = self.generator.generate_password()
                    if new_pass:
                        combined_passwords.append(new_pass)
            else:
                Logger.log_error("Generative mode enabled, but model is not trained. Use --train-generator first.")
        
        if not combined_passwords:
            Logger.log_error("No passwords to test.")
            return

        if self.use_scorer:
            if self.scorer and self.scorer.model:
                final_passwords = self.scorer.sort_passwords(combined_passwords)
            else:
                Logger.log_error("Scorer mode enabled, but model is not trained. Use --train-scorer first.")
                final_passwords = combined_passwords
        else:
            final_passwords = combined_passwords
        
        # --- NEW LOGGING ---
        Logger.log_info(f"Populating queue with {len(final_passwords)} passwords...")
        for p in final_passwords:
            self.password_queue.put(p)
        Logger.log_info("Queue populated. Starting worker threads...")

        for i in range(self.threads):
            thread = threading.Thread(target=self._worker, args=(i+1,), daemon=True)
            thread.start()
            self.thread_pool.append(thread)
        Logger.log_info(f"{self.threads} worker threads launched. Monitoring attack.")

        while True:
            if self.stop_event.is_set():
                break
            if not any(t.is_alive() for t in self.thread_pool):
                break
            time.sleep(0.5)

        self.stop_event.set()