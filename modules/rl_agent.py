# modules/rl_agent.py

import json
import os
import random
from collections import defaultdict
from modules.logger import Logger

MODEL_DIR = "learned_models"
PREFIX_LEN = 3 # Analyze prefixes of up to this length

class RLAgent:
    def __init__(self, target_id):
        self.target_id = target_id
        self.model_path = os.path.join(MODEL_DIR, f"{self.target_id.replace('://', '_').replace('/', '_')}.json")
        self.scores = defaultdict(float)
        self.epsilon = 0.1 # 10% chance of exploration (trying a random password)
        self.load_model()

    def load_model(self):
        if os.path.exists(self.model_path):
            try:
                with open(self.model_path, 'r') as f:
                    self.scores = defaultdict(float, json.load(f))
                Logger.log_info(f"Loaded learned patterns for target: {self.target_id}")
            except (json.JSONDecodeError, IOError) as e:
                Logger.log_error(f"Could not load model file {self.model_path}: {e}")
                self.scores = defaultdict(float)
        else:
            Logger.log_info("No previous model found. Starting fresh.")

    def save_model(self):
        if not os.path.exists(MODEL_DIR):
            os.makedirs(MODEL_DIR)
        try:
            with open(self.model_path, 'w') as f:
                json.dump(dict(self.scores), f, indent=2)
            Logger.log_info(f"Saved learned patterns to {self.model_path}")
        except IOError as e:
            Logger.log_error(f"Could not save model file: {e}")
            
    def _get_password_score(self, password):
        """Calculates a score for a password based on its prefixes."""
        score = 0.0
        for i in range(1, min(len(password), PREFIX_LEN) + 1):
            prefix = password[:i]
            score += self.scores.get(prefix, 0.0)
        return score

    def update_scores(self, password, success):
        """Update scores for prefixes based on login attempt outcome."""
        reward = 100.0 if success else -1.0
        
        # Apply learning rate alpha (e.g., 0.1) to the update
        alpha = 0.1
        for i in range(1, min(len(password), PREFIX_LEN) + 1):
            prefix = password[:i]
            current_score = self.scores.get(prefix, 0.0)
            self.scores[prefix] = current_score + alpha * (reward - current_score)

    def sort_passwords(self, passwords):
        """Sorts the password list based on learned scores."""
        Logger.log_info("Applying RL model to prioritize password list...")
        
        # Epsilon-greedy strategy applied to the whole list
        if random.random() < self.epsilon:
            Logger.log_info("Exploring: Shuffling password list randomly.")
            random.shuffle(passwords)
            return passwords
        
        # Sort by score (descending)
        sorted_passwords = sorted(passwords, key=self._get_password_score, reverse=True)
        return sorted_passwords
