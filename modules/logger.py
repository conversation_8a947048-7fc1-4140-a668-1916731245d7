# modules/logger.py

import sys
from colorama import Fore, Style, init

# Initialize colorama
init()

class Logger:
    @staticmethod
    def log_info(message):
        print(f"{Fore.BLUE}[*]{Style.RESET_ALL} {message}")

    @staticmethod
    def log_success(message):
        print(f"\n{Fore.GREEN}[+]{Style.RESET_ALL} {Style.BRIGHT}{message}{Style.RESET_ALL}")

    @staticmethod
    def log_error(message):
        print(f"{Fore.RED}[-]{Style.RESET_ALL} {message}")

    @staticmethod
    def log_attempt(ip, user, password, thread_id):
        status_line = (
            f"{Fore.YELLOW}[ATTEMPT]{Style.RESET_ALL} "
            f"Target: {ip} | "
            f"Login: {user} | "
            f"Pass: {password} | "
            f"Thread: {thread_id}"
        )
        # Use carriage return to overwrite the line
        sys.stdout.write("\r" + " " * 80) # Clear previous line
        sys.stdout.write("\r" + status_line)
        sys.stdout.flush()
