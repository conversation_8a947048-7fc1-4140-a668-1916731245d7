# Create this new file: modules/password_generator.py

import json
import os
import random
from collections import defaultdict
from modules.logger import Logger

class PasswordGenerator:
    def __init__(self, model_path="learned_models/markov_model.json"):
        self.model_path = model_path
        self.model = None
        self.load_model()

    def train(self, wordlist_path, state_size=2):
        """Analyzes a wordlist and builds the Markov chain model."""
        Logger.log_info(f"Training Markov model from {wordlist_path}...")
        transitions = defaultdict(lambda: defaultdict(int))
        
        with open(wordlist_path, 'r', encoding='latin-1') as f:
            for line in f:
                password = line.strip()
                if len(password) <= state_size:
                    continue
                
                for i in range(len(password) - state_size):
                    state = password[i:i+state_size]
                    next_char = password[i+state_size]
                    transitions[state][next_char] += 1
        
        self.model = {
            "state_size": state_size,
            "transitions": dict(transitions)
        }
        self._save_model()
        Logger.log_success(f"Training complete. Model saved to {self.model_path}")

    def _save_model(self):
        if not os.path.exists("learned_models"):
            os.makedirs("learned_models")
        with open(self.model_path, 'w') as f:
            json.dump(self.model, f)

    def load_model(self):
        if os.path.exists(self.model_path):
            try:
                with open(self.model_path, 'r') as f:
                    self.model = json.load(f)
                Logger.log_info("Markov chain password generator model loaded.")
            except (FileNotFoundError, json.JSONDecodeError):
                self.model = None
        else:
            self.model = None

    def generate_password(self, max_len=12):
        """Generates a new password from scratch using the model."""
        if not self.model:
            return None

        # Start with one of the most common initial states
        start_states = list(self.model['transitions'].keys())
        if not start_states:
            return None # Model is empty
        
        current_state = random.choice(start_states)
        password = list(current_state)
        state_size = self.model['state_size']

        while len(password) < max_len:
            if current_state not in self.model['transitions']:
                break
            
            possible_next = self.model['transitions'][current_state]
            choices, weights = zip(*possible_next.items())
            next_char = random.choices(choices, weights=weights, k=1)[0]
            
            password.append(next_char)
            current_state = "".join(password[-state_size:])
            
        return "".join(password)