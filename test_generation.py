#!/usr/bin/env python3

from modules.password_generator import PasswordGenerator
from modules.password_scorer import PasswordScorer

def test_password_generation():
    print("=== Testing Password Generation ===")
    
    # Test the generator
    generator = PasswordGenerator()
    if generator.model:
        print("✓ Generator model loaded successfully")
        print("\nGenerating 10 sample passwords:")
        for i in range(10):
            password = generator.generate_password()
            print(f"  {i+1}: {password}")
    else:
        print("✗ Generator model not found")
    
    print("\n=== Testing Password Scoring ===")
    
    # Test the scorer
    scorer = PasswordScorer()
    if scorer.model:
        print("✓ Scorer model loaded successfully")
        
        # Test passwords from our training set
        test_passwords = ["password", "123456", "admin", "qwerty", "randomword", "xyz123"]
        print("\nScoring test passwords:")
        scored = []
        for pwd in test_passwords:
            score = scorer.score(pwd)
            scored.append((score, pwd))
            print(f"  {pwd:<12} -> {score:.4f}")
        
        print("\nSorted by score (highest first):")
        scored.sort(key=lambda x: x[0], reverse=True)
        for score, pwd in scored:
            print(f"  {pwd:<12} -> {score:.4f}")
            
    else:
        print("✗ Scorer model not found")

if __name__ == "__main__":
    test_password_generation()
